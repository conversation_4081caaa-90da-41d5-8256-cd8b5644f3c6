[2025-07-16 06:38:37] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'assets_manager.users' doesn't exist (Connection: mysql, SQL: select exists(select * from `users` where `email` = <EMAIL>) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'assets_manager.users' doesn't exist (Connection: mysql, SQL: select exists(select * from `users` where `email` = <EMAIL>) as `exists`) at /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists(s...', <PERSON><PERSON><PERSON>, Object(Closure))
#1 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select exists(s...', Array, Object(Closure))
#2 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3504): Illuminate\\Database\\Connection->select('select exists(s...', Array, true)
#3 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(2205): Illuminate\\Database\\Query\\Builder->exists()
#4 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/filament/filament/src/Commands/MakeUserCommand.php(48): Illuminate\\Database\\Eloquent\\Builder->__call('exists', Array)
#5 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/prompts/src/Prompt.php(406): Filament\\Commands\\MakeUserCommand->{closure:Filament\\Commands\\MakeUserCommand::getUserData():46}('<EMAIL>')
#6 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/prompts/src/Prompt.php(316): Laravel\\Prompts\\Prompt->validate('<EMAIL>')
#7 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/prompts/src/Concerns/TypedValue.php(58): Laravel\\Prompts\\Prompt->submit()
#8 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/prompts/src/Concerns/Events.php(30): Laravel\\Prompts\\TextPrompt->{closure:Laravel\\Prompts\\Concerns\\TypedValue::trackTypedValue():30}('\\n')
#9 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/prompts/src/Prompt.php(332): Laravel\\Prompts\\Prompt->emit('key', '\\n')
#10 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/prompts/src/Prompt.php(132): Laravel\\Prompts\\Prompt->handleKeyPress('\\n')
#11 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/prompts/src/Prompt.php(179): Laravel\\Prompts\\Prompt->{closure:Laravel\\Prompts\\Prompt::prompt():131}('\\n')
#12 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/prompts/src/Prompt.php(131): Laravel\\Prompts\\Prompt->runLoop(Object(Closure))
#13 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/prompts/src/helpers.php(21): Laravel\\Prompts\\Prompt->prompt()
#14 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/filament/filament/src/Commands/MakeUserCommand.php(43): Laravel\\Prompts\\text('Email address', '', '', true, Object(Closure))
#15 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/filament/filament/src/Commands/MakeUserCommand.php(62): Filament\\Commands\\MakeUserCommand->getUserData()
#16 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/filament/filament/src/Commands/MakeUserCommand.php(100): Filament\\Commands\\MakeUserCommand->createUser()
#17 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Filament\\Commands\\MakeUserCommand->handle()
#18 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#19 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#23 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Filament\\Commands\\MakeUserCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 /Users/<USER>/DEHA-Projects/assets/assets-manager/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#31 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'assets_manager.users' doesn't exist at /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php:404)
[stacktrace]
#0 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): PDO->prepare('select exists(s...')
#1 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():395}('select exists(s...', Array)
#2 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists(s...', Array, Object(Closure))
#3 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select exists(s...', Array, Object(Closure))
#4 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3504): Illuminate\\Database\\Connection->select('select exists(s...', Array, true)
#5 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(2205): Illuminate\\Database\\Query\\Builder->exists()
#6 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/filament/filament/src/Commands/MakeUserCommand.php(48): Illuminate\\Database\\Eloquent\\Builder->__call('exists', Array)
#7 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/prompts/src/Prompt.php(406): Filament\\Commands\\MakeUserCommand->{closure:Filament\\Commands\\MakeUserCommand::getUserData():46}('<EMAIL>')
#8 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/prompts/src/Prompt.php(316): Laravel\\Prompts\\Prompt->validate('<EMAIL>')
#9 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/prompts/src/Concerns/TypedValue.php(58): Laravel\\Prompts\\Prompt->submit()
#10 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/prompts/src/Concerns/Events.php(30): Laravel\\Prompts\\TextPrompt->{closure:Laravel\\Prompts\\Concerns\\TypedValue::trackTypedValue():30}('\\n')
#11 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/prompts/src/Prompt.php(332): Laravel\\Prompts\\Prompt->emit('key', '\\n')
#12 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/prompts/src/Prompt.php(132): Laravel\\Prompts\\Prompt->handleKeyPress('\\n')
#13 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/prompts/src/Prompt.php(179): Laravel\\Prompts\\Prompt->{closure:Laravel\\Prompts\\Prompt::prompt():131}('\\n')
#14 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/prompts/src/Prompt.php(131): Laravel\\Prompts\\Prompt->runLoop(Object(Closure))
#15 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/prompts/src/helpers.php(21): Laravel\\Prompts\\Prompt->prompt()
#16 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/filament/filament/src/Commands/MakeUserCommand.php(43): Laravel\\Prompts\\text('Email address', '', '', true, Object(Closure))
#17 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/filament/filament/src/Commands/MakeUserCommand.php(62): Filament\\Commands\\MakeUserCommand->getUserData()
#18 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/filament/filament/src/Commands/MakeUserCommand.php(100): Filament\\Commands\\MakeUserCommand->createUser()
#19 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Filament\\Commands\\MakeUserCommand->handle()
#20 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#21 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Filament\\Commands\\MakeUserCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Users/<USER>/DEHA-Projects/assets/assets-manager/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#33 {main}
"} 
[2025-07-16 06:42:59] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP 'sessions_user_id_foreign'; check that column/key exists (Connection: mysql, SQL: alter table `sessions` drop foreign key `sessions_user_id_foreign`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP 'sessions_user_id_foreign'; check that column/key exists (Connection: mysql, SQL: alter table `sessions` drop foreign key `sessions_user_id_foreign`) at /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `se...', Array, Object(Closure))
#1 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('alter table `se...', Array, Object(Closure))
#2 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `se...')
#3 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('sessions', Object(Closure))
#6 /Users/<USER>/DEHA-Projects/assets/assets-manager/database/migrations/2025_07_16_064109_modify_users_table_for_asset_management.php(34): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#7 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runMigration():435}()
#10 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(41): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runUp():250}()
#12 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_16_0641...', Object(Closure))
#13 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_16_0641...', Object(Closure))
#14 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('/Users/<USER>/DE...', 2, false)
#15 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():109}()
#18 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#22 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /Users/<USER>/DEHA-Projects/assets/assets-manager/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP 'sessions_user_id_foreign'; check that column/key exists at /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php:568)
[stacktrace]
#0 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php(568): PDOStatement->execute()
#1 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():557}('alter table `se...', Array)
#2 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `se...', Array, Object(Closure))
#3 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('alter table `se...', Array, Object(Closure))
#4 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `se...')
#5 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('sessions', Object(Closure))
#8 /Users/<USER>/DEHA-Projects/assets/assets-manager/database/migrations/2025_07_16_064109_modify_users_table_for_asset_management.php(34): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#9 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runMigration():435}()
#12 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(41): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runUp():250}()
#14 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_16_0641...', Object(Closure))
#15 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_16_0641...', Object(Closure))
#16 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('/Users/<USER>/DE...', 2, false)
#17 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->{closure:Illuminate\\Database\\Console\\Migrations\\MigrateCommand::runMigrations():109}()
#20 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#24 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /Users/<USER>/DEHA-Projects/assets/assets-manager/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-07-16 06:43:30] local.ERROR: SQLSTATE[HY000]: General error: 3730 Cannot drop table 'positions' referenced by a foreign key constraint 'users_position_id_foreign' on table 'users'. (Connection: mysql, SQL: drop table if exists `positions`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 3730 Cannot drop table 'positions' referenced by a foreign key constraint 'users_position_id_foreign' on table 'users'. (Connection: mysql, SQL: drop table if exists `positions`) at /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('drop table if e...', Array, Object(Closure))
#1 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('drop table if e...', Array, Object(Closure))
#2 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(119): Illuminate\\Database\\Connection->statement('drop table if e...')
#3 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(500): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->dropIfExists('positions')
#6 /Users/<USER>/DEHA-Projects/assets/assets-manager/database/migrations/2025_07_16_064040_create_positions_table.php(35): Illuminate\\Support\\Facades\\Facade::__callStatic('dropIfExists', Array)
#7 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->down()
#8 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#9 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runMigration():435}()
#10 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(414): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#11 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(41): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runDown():414}()
#12 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_16_0640...', Object(Closure))
#13 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(414): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_16_0640...', Object(Closure))
#14 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(337): Illuminate\\Database\\Migrations\\Migrator->runDown('/Users/<USER>/DE...', Object(stdClass), false)
#15 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(281): Illuminate\\Database\\Migrations\\Migrator->rollbackMigrations(Array, Array, Array)
#16 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/RollbackCommand.php(63): Illuminate\\Database\\Migrations\\Migrator->rollback(Array, Array)
#17 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\RollbackCommand->{closure:Illuminate\\Database\\Console\\Migrations\\RollbackCommand::handle():62}()
#18 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/RollbackCommand.php(62): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RollbackCommand->handle()
#20 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#21 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#22 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#23 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#24 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#25 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RollbackCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Users/<USER>/DEHA-Projects/assets/assets-manager/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#33 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 3730 Cannot drop table 'positions' referenced by a foreign key constraint 'users_position_id_foreign' on table 'users'. at /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php:568)
[stacktrace]
#0 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php(568): PDOStatement->execute()
#1 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():557}('drop table if e...', Array)
#2 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('drop table if e...', Array, Object(Closure))
#3 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('drop table if e...', Array, Object(Closure))
#4 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(119): Illuminate\\Database\\Connection->statement('drop table if e...')
#5 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(500): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->dropIfExists('positions')
#8 /Users/<USER>/DEHA-Projects/assets/assets-manager/database/migrations/2025_07_16_064040_create_positions_table.php(35): Illuminate\\Support\\Facades\\Facade::__callStatic('dropIfExists', Array)
#9 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->down()
#10 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#11 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runMigration():435}()
#12 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(414): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'down')
#13 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(41): Illuminate\\Database\\Migrations\\Migrator->{closure:Illuminate\\Database\\Migrations\\Migrator::runDown():414}()
#14 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_07_16_0640...', Object(Closure))
#15 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(414): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_07_16_0640...', Object(Closure))
#16 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(337): Illuminate\\Database\\Migrations\\Migrator->runDown('/Users/<USER>/DE...', Object(stdClass), false)
#17 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(281): Illuminate\\Database\\Migrations\\Migrator->rollbackMigrations(Array, Array, Array)
#18 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/RollbackCommand.php(63): Illuminate\\Database\\Migrations\\Migrator->rollback(Array, Array)
#19 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\RollbackCommand->{closure:Illuminate\\Database\\Console\\Migrations\\RollbackCommand::handle():62}()
#20 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/RollbackCommand.php(62): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\RollbackCommand->handle()
#22 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#23 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Console/Command.php(211): Illuminate\\Container\\Container->call(Array)
#27 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Console/Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\RollbackCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /Users/<USER>/DEHA-Projects/assets/assets-manager/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 /Users/<USER>/DEHA-Projects/assets/assets-manager/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#35 {main}
"} 
