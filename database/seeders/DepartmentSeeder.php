<?php

namespace Database\Seeders;

use App\Models\Department;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DepartmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $departments = [
            [
                'name' => 'Information Technology',
                'code' => 'IT',
                'description' => 'Responsible for technology infrastructure and software development',
                'manager_name' => '<PERSON>',
                'location' => 'Floor 3',
                'is_active' => true,
            ],
            [
                'name' => 'Human Resources',
                'code' => 'HR',
                'description' => 'Manages employee relations and organizational development',
                'manager_name' => '<PERSON>',
                'location' => 'Floor 2',
                'is_active' => true,
            ],
            [
                'name' => 'Finance & Accounting',
                'code' => 'FIN',
                'description' => 'Handles financial planning and accounting operations',
                'manager_name' => '<PERSON>',
                'location' => 'Floor 1',
                'is_active' => true,
            ],
            [
                'name' => 'Marketing',
                'code' => 'MKT',
                'description' => 'Manages marketing campaigns and brand development',
                'manager_name' => '<PERSON>',
                'location' => 'Floor 4',
                'is_active' => true,
            ],
            [
                'name' => 'Operations',
                'code' => 'OPS',
                'description' => 'Oversees daily operations and process improvement',
                'manager_name' => 'David Brown',
                'location' => 'Floor 2',
                'is_active' => true,
            ],
        ];

        foreach ($departments as $department) {
            Department::create($department);
        }
    }
}
