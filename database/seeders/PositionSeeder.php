<?php

namespace Database\Seeders;

use App\Models\Department;
use App\Models\Position;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PositionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $itDept = Department::where('code', 'IT')->first();
        $hrDept = Department::where('code', 'HR')->first();
        $finDept = Department::where('code', 'FIN')->first();
        $mktDept = Department::where('code', 'MKT')->first();
        $opsDept = Department::where('code', 'OPS')->first();

        $positions = [
            // IT Department
            [
                'title' => 'Software Developer',
                'code' => 'IT-DEV',
                'description' => 'Develops and maintains software applications',
                'level' => 'senior',
                'department_id' => $itDept->id,
                'is_active' => true,
            ],
            [
                'title' => 'System Administrator',
                'code' => 'IT-SYSADMIN',
                'description' => 'Manages IT infrastructure and systems',
                'level' => 'senior',
                'department_id' => $itDept->id,
                'is_active' => true,
            ],
            [
                'title' => 'IT Support Specialist',
                'code' => 'IT-SUPPORT',
                'description' => 'Provides technical support to users',
                'level' => 'junior',
                'department_id' => $itDept->id,
                'is_active' => true,
            ],

            // HR Department
            [
                'title' => 'HR Manager',
                'code' => 'HR-MGR',
                'description' => 'Manages human resources operations',
                'level' => 'manager',
                'department_id' => $hrDept->id,
                'is_active' => true,
            ],
            [
                'title' => 'HR Specialist',
                'code' => 'HR-SPEC',
                'description' => 'Handles recruitment and employee relations',
                'level' => 'senior',
                'department_id' => $hrDept->id,
                'is_active' => true,
            ],

            // Finance Department
            [
                'title' => 'Accountant',
                'code' => 'FIN-ACC',
                'description' => 'Manages financial records and transactions',
                'level' => 'senior',
                'department_id' => $finDept->id,
                'is_active' => true,
            ],

            // Marketing Department
            [
                'title' => 'Marketing Specialist',
                'code' => 'MKT-SPEC',
                'description' => 'Develops and executes marketing campaigns',
                'level' => 'senior',
                'department_id' => $mktDept->id,
                'is_active' => true,
            ],

            // Operations Department
            [
                'title' => 'Operations Manager',
                'code' => 'OPS-MGR',
                'description' => 'Oversees daily operations',
                'level' => 'manager',
                'department_id' => $opsDept->id,
                'is_active' => true,
            ],
        ];

        foreach ($positions as $position) {
            Position::create($position);
        }
    }
}
