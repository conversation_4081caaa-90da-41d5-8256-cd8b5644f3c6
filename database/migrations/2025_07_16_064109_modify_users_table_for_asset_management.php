<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add company-specific fields
            $table->string('employee_id')->unique()->nullable()->after('id');
            $table->string('keycloak_id')->unique()->nullable()->after('employee_id');
            $table->string('full_name')->nullable()->after('name');
            $table->foreignUuid('department_id')->nullable()->constrained()->onDelete('set null')->after('full_name');
            $table->foreignUuid('position_id')->nullable()->constrained()->onDelete('set null')->after('department_id');
            $table->enum('role', ['admin', 'manager', 'user'])->default('user')->after('position_id');
            $table->boolean('is_active')->default(true)->after('role');
            $table->timestamp('last_login_at')->nullable()->after('is_active');
            $table->json('preferences')->nullable()->after('last_login_at');

            // Add indexes for performance
            $table->index('employee_id');
            $table->index(['department_id', 'is_active']);
            $table->index(['role', 'is_active']);
            $table->index('keycloak_id');
        });

        // Update sessions table to use string for user_id (no foreign key exists)
        Schema::table('sessions', function (Blueprint $table) {
            $table->dropIndex(['user_id']);
            $table->string('user_id')->nullable()->change();
            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['employee_id']);
            $table->dropIndex(['department_id', 'is_active']);
            $table->dropIndex(['role', 'is_active']);
            $table->dropIndex(['keycloak_id']);

            $table->dropForeign(['department_id']);
            $table->dropForeign(['position_id']);

            $table->dropColumn([
                'employee_id',
                'keycloak_id',
                'full_name',
                'department_id',
                'position_id',
                'role',
                'is_active',
                'last_login_at',
                'preferences'
            ]);
        });

        Schema::table('sessions', function (Blueprint $table) {
            $table->dropIndex(['user_id']);
            $table->unsignedBigInteger('user_id')->nullable()->change();
            $table->index('user_id');
        });
    }
};
