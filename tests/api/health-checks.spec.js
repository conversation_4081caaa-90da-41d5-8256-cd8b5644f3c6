import { test, expect } from "@playwright/test";

test.describe("API Health Checks", () => {
    test("Application status check", async ({ request }) => {
        // Test root endpoint
        const response = await request.get("/");
        expect(response.status()).toBeLessThan(500);

        // Should return HTML content
        const contentType = response.headers()["content-type"];
        expect(contentType).toContain("text/html");
    });

    test("Admin panel API status", async ({ request }) => {
        // Test admin login endpoint
        const response = await request.get("/admin/login");
        expect(response.status()).toBe(200);

        // Should return HTML login page
        const body = await response.text();
        expect(body).toContain("login");
        expect(body.length).toBeGreaterThan(100);
    });

    test("Database connection via API", async ({ request }) => {
        // Test an endpoint that requires database access
        const response = await request.post("/admin/login", {
            form: {
                email: "<EMAIL>",
                password: "wrongpassword",
                _token: "dummy-token", // This will fail CSRF but should reach DB validation
            },
        });

        // Should get a response (not 500 error which would indicate DB issues)
        expect(response.status()).toBeLessThan(500);

        // Should be either 422 (validation) or 419 (CSRF) - both indicate DB is accessible
        expect([419, 422, 302]).toContain(response.status());
    });

    test("Static assets availability", async ({ request }) => {
        // Test that compiled assets are available
        const cssResponse = await request.get("/build/assets/app-CmHeOvdh.css");
        if (cssResponse.status() !== 404) {
            expect(cssResponse.status()).toBe(200);
            expect(cssResponse.headers()["content-type"]).toContain("text/css");
        }

        // Test Filament assets
        const filamentResponse = await request.get(
            "/css/filament/filament/app.css"
        );
        if (filamentResponse.status() !== 404) {
            expect(filamentResponse.status()).toBe(200);
        }
    });

    test("Error handling", async ({ request }) => {
        // Test 404 handling
        const notFoundResponse = await request.get("/api/nonexistent-endpoint");
        expect(notFoundResponse.status()).toBe(404);

        // Test method not allowed
        const methodNotAllowedResponse = await request.patch("/admin/login");
        expect([404, 405]).toContain(methodNotAllowedResponse.status());
    });

    test("Security headers", async ({ request }) => {
        const response = await request.get("/admin/login");
        const headers = response.headers();

        // Basic security checks
        expect(headers["content-type"]).toBeTruthy();
        expect(headers["content-type"]).toContain("text/html");

        // Should not expose sensitive information
        expect(headers["server"]).not.toContain("Apache/");
        expect(headers["x-powered-by"]).toBeFalsy();
    });

    test("CSRF protection", async ({ request }) => {
        // Test that CSRF protection is active
        const response = await request.post("/admin/login", {
            form: {
                email: "<EMAIL>",
                password: "password",
                // No CSRF token
            },
        });

        // Should reject request without CSRF token
        expect(response.status()).toBe(419); // CSRF token mismatch
    });

    test("Rate limiting basic check", async ({ request }) => {
        // Make multiple requests to check basic rate limiting
        const responses = [];

        for (let i = 0; i < 5; i++) {
            const response = await request.get("/admin/login");
            responses.push(response.status());
        }

        // All requests should succeed (no rate limiting on GET login page)
        responses.forEach((status) => {
            expect(status).toBe(200);
        });
    });
});
