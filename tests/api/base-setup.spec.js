import { test, expect } from "@playwright/test";

test.describe("Base Setup API Tests", () => {
    test("Environment variables validation", async ({ request }) => {
        // Test that the application is running with correct environment
        const response = await request.get("/admin/login");
        expect(response.status()).toBe(200);

        const body = await response.text();

        // Should contain Assets Manager branding
        expect(body).toContain("Assets Manager");

        // Should be in development mode (debug enabled)
        // In production, error pages would be different
        const errorResponse = await request.get("/nonexistent-route");
        expect(errorResponse.status()).toBe(404);
    });

    test("File permissions check", async ({ request }) => {
        // Test that storage directories are writable by checking session functionality
        const loginResponse = await request.get("/admin/login");
        expect(loginResponse.status()).toBe(200);

        // Check that sessions are working (requires writable storage)
        const cookies = loginResponse.headers()["set-cookie"];
        expect(cookies).toBeTruthy();

        // Should set Laravel session cookie
        const sessionCookie = cookies?.find((cookie) =>
            cookie.includes("laravel_session")
        );
        expect(sessionCookie).toBeTruthy();
    });

    test("Cache configuration validation", async ({ request }) => {
        // Test cache functionality through repeated requests
        const start1 = Date.now();
        const response1 = await request.get("/admin/login");
        const time1 = Date.now() - start1;

        expect(response1.status()).toBe(200);

        const start2 = Date.now();
        const response2 = await request.get("/admin/login");
        const time2 = Date.now() - start2;

        expect(response2.status()).toBe(200);

        // Both requests should complete successfully
        expect(time1).toBeGreaterThan(0);
        expect(time2).toBeGreaterThan(0);
    });

    test("Database migrations status", async ({ request }) => {
        // Test that database is properly migrated by attempting login
        const response = await request.post("/admin/login", {
            form: {
                email: "<EMAIL>",
                password: "wrongpassword",
            },
        });

        // Should get CSRF error (419) or validation error (422), not DB error (500)
        expect([419, 422, 302]).toContain(response.status());
        expect(response.status()).not.toBe(500);
    });

    test("Seeded data availability", async ({ request, page }) => {
        // Test that seeded data exists by logging in with seeded admin user
        await page.goto("/admin/login");

        // Get CSRF token
        const csrfToken = await page
            .locator('input[name="_token"]')
            .getAttribute("value");

        const response = await request.post("/admin/login", {
            form: {
                email: "<EMAIL>",
                password: "password",
                _token: csrfToken,
            },
        });

        // Should successfully login (redirect to admin dashboard)
        expect(response.status()).toBe(302);

        const location = response.headers()["location"];
        expect(location).toContain("/admin");
    });

    test("Filament installation validation", async ({ request }) => {
        // Test that Filament is properly installed
        const response = await request.get("/admin/login");
        expect(response.status()).toBe(200);

        const body = await response.text();

        // Should contain Filament-specific elements
        expect(body).toContain("filament");

        // Should have proper form structure
        expect(body).toContain("input");
        expect(body).toContain('type="email"');
        expect(body).toContain('type="password"');
    });

    test("Laravel Sanctum configuration", async ({ request }) => {
        // Test that Sanctum is installed (even if not fully configured yet)
        const response = await request.get("/sanctum/csrf-cookie");

        // Should either work (200) or be not found (404), but not error (500)
        expect([200, 404]).toContain(response.status());
    });

    test("Middleware stack validation", async ({ request }) => {
        // Test that middleware stack is working

        // Test CSRF middleware
        const csrfResponse = await request.post("/admin/login", {
            form: {
                email: "<EMAIL>",
                password: "test",
            },
        });
        expect(csrfResponse.status()).toBe(419); // CSRF token mismatch

        // Test authentication middleware
        const authResponse = await request.get("/admin");
        expect(authResponse.status()).toBe(302); // Redirect to login

        const location = authResponse.headers()["location"];
        expect(location).toContain("login");
    });

    test("Asset compilation validation", async ({ request }) => {
        // Test that assets are compiled and accessible
        const manifestResponse = await request.get("/build/manifest.json");

        if (manifestResponse.status() === 200) {
            const manifest = await manifestResponse.json();
            expect(manifest).toBeTruthy();
            expect(typeof manifest).toBe("object");
        }

        // Test that at least some CSS is available
        const response = await request.get("/admin/login");
        const body = await response.text();

        // Should have CSS links or inline styles
        const hasCss =
            body.includes("<link") ||
            body.includes("<style") ||
            body.includes("css");
        expect(hasCss).toBeTruthy();
    });

    test("Error handling configuration", async ({ request }) => {
        // Test custom error pages
        const response = await request.get("/nonexistent-route-test-12345");
        expect(response.status()).toBe(404);

        const body = await response.text();
        expect(body.length).toBeGreaterThan(50); // Should have error page content

        // Test that 500 errors are handled gracefully
        // This is hard to test without breaking something, so we'll skip for now
    });
});
