import { test, expect } from "@playwright/test";

test.describe("Admin Panel Tests", () => {
    test("Admin panel accessibility", async ({ page }) => {
        // Navigate to admin panel
        await page.goto("/admin");

        // Should redirect to login if not authenticated
        await expect(page).toHaveURL(/.*\/admin\/login/);

        // Check that login page is accessible
        await expect(page).toHaveTitle(/Login/);
        await expect(
            page.locator("h1, h2").filter({ hasText: /login/i })
        ).toBeVisible();
    });

    test("Login page functionality", async ({ page }) => {
        await page.goto("/admin/login");

        // Check form elements are present
        await expect(page.locator('input[type="email"]')).toBeVisible();
        await expect(page.locator('input[type="password"]')).toBeVisible();
        await expect(page.locator('button[type="submit"]')).toBeVisible();

        // Test form validation
        await page.click('button[type="submit"]');

        // Should show validation errors
        await expect(page.locator("text=required")).toBeVisible({
            timeout: 3000,
        });

        // Test with invalid credentials
        await page.fill('input[type="email"]', "<EMAIL>");
        await page.fill('input[type="password"]', "wrongpassword");
        await page.click('button[type="submit"]');

        // Should show authentication error
        await expect(page.locator("text=credentials")).toBeVisible({
            timeout: 5000,
        });
    });

    test("Successful admin login and basic navigation", async ({ page }) => {
        await page.goto("/admin/login");

        // Login with valid credentials
        await page.fill('input[type="email"]', "<EMAIL>");
        await page.fill('input[type="password"]', "password");
        await page.click('button[type="submit"]');

        // Should redirect to dashboard
        await expect(page).toHaveURL(/.*\/admin$/);

        // Check dashboard elements
        await expect(page.locator("text=Dashboard")).toBeVisible();

        // Check navigation sidebar
        await expect(page.locator("nav")).toBeVisible();

        // Check user menu/profile
        await expect(
            page.locator(
                '[data-testid="user-menu"], .fi-user-menu, text=Admin User'
            )
        ).toBeVisible();
    });

    test("Admin panel branding and layout", async ({ page }) => {
        // Login first
        await page.goto("/admin/login");
        await page.fill('input[type="email"]', "<EMAIL>");
        await page.fill('input[type="password"]', "password");
        await page.click('button[type="submit"]');

        await expect(page).toHaveURL(/.*\/admin$/);

        // Check branding
        await expect(page.locator("text=Assets Manager")).toBeVisible();

        // Check responsive layout
        const viewport = page.viewportSize();
        expect(viewport.width).toBeGreaterThan(0);
        expect(viewport.height).toBeGreaterThan(0);

        // Check that sidebar is collapsible (desktop feature)
        if (viewport.width > 1024) {
            // Look for sidebar toggle or collapsible elements
            const sidebar = page.locator('aside, nav[role="navigation"]');
            await expect(sidebar).toBeVisible();
        }
    });

    test("Admin panel logout functionality", async ({ page }) => {
        // Login first
        await page.goto("/admin/login");
        await page.fill('input[type="email"]', "<EMAIL>");
        await page.fill('input[type="password"]', "password");
        await page.click('button[type="submit"]');

        await expect(page).toHaveURL(/.*\/admin$/);

        // Find and click logout
        // Try different possible logout selectors
        const logoutSelectors = [
            "text=Logout",
            "text=Sign out",
            '[data-testid="logout"]',
            'button:has-text("Logout")',
            'a:has-text("Logout")',
        ];

        let loggedOut = false;
        for (const selector of logoutSelectors) {
            try {
                const element = page.locator(selector);
                if (await element.isVisible({ timeout: 1000 })) {
                    await element.click();
                    loggedOut = true;
                    break;
                }
            } catch (e) {
                // Continue to next selector
            }
        }

        // If no logout button found, try user menu dropdown
        if (!loggedOut) {
            try {
                await page.click('[data-testid="user-menu"], .fi-user-menu');
                await page.click("text=Logout, text=Sign out");
                loggedOut = true;
            } catch (e) {
                // Logout test will be skipped if no logout button found
                console.log("Logout button not found, skipping logout test");
                return;
            }
        }

        // Should redirect to login page
        await expect(page).toHaveURL(/.*\/admin\/login/);
    });
});
