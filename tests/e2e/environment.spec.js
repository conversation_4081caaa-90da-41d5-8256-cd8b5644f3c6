import { test, expect } from "@playwright/test";

test.describe("Environment Tests", () => {
    test("Application health check", async ({ page }) => {
        // Navigate to the application root
        await page.goto("/");

        // Check that the page loads successfully
        await expect(page).toHaveTitle(/Laravel/);

        // Check for Laravel welcome page or redirect
        const hasWelcome = await page.locator("text=Laravel").isVisible();
        const hasRedirect = page.url().includes("/admin");

        expect(hasWelcome || hasRedirect).toBeTruthy();
    });

    test("Database connection verification", async ({ page }) => {
        // Create a simple API endpoint test or check admin login which requires DB
        await page.goto("/admin/login");

        // If login page loads, database connection is working
        await expect(page).toHaveTitle(/Login/);
        await expect(page.locator('input[type="email"]')).toBeVisible();
        await expect(page.locator('input[type="password"]')).toBeVisible();
    });

    test("Application configuration test", async ({ page }) => {
        // Test that the application is properly configured
        await page.goto("/");

        // Check that we don't get any 500 errors
        const response = await page.goto("/");
        expect(response.status()).toBeLessThan(500);

        // Check that assets are loading properly
        await page.goto("/admin/login");

        // Wait for CSS to load
        await page.waitForLoadState("networkidle");

        // Check that Filament styles are applied
        const bodyClass = await page.locator("body").getAttribute("class");
        expect(bodyClass).toBeTruthy();
    });

    test("Service availability test", async ({ page }) => {
        // Test that core services are available
        await page.goto("/admin/login");

        // Check that the login form is functional (services working)
        await expect(page.locator("form").first()).toBeVisible();
        await expect(page.locator('button[type="submit"]')).toBeVisible();

        // Test form submission (this will fail auth but proves services work)
        await page.fill('input[type="email"]', "<EMAIL>");
        await page.fill('input[type="password"]', "wrongpassword");
        await page.click('button[type="submit"]');

        // Should get validation error, not service error
        await expect(page.locator("text=credentials")).toBeVisible({
            timeout: 5000,
        });
    });

    test("Core functionality test", async ({ page }) => {
        // Test that core Laravel functionality works
        await page.goto("/admin/login");

        // Check CSRF protection is working
        const csrfToken = await page
            .locator('input[name="_token"]')
            .first()
            .getAttribute("value");
        expect(csrfToken).toBeTruthy();
        expect(csrfToken.length).toBeGreaterThan(10);

        // Check that session is working
        await page.reload();
        const newCsrfToken = await page
            .locator('input[name="_token"]')
            .first()
            .getAttribute("value");
        expect(newCsrfToken).toBeTruthy();
        // CSRF token should change on reload (session working)
    });
});
