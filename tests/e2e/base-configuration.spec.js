import { test, expect } from "@playwright/test";

test.describe("Base Configuration Tests", () => {
    test("Environment configuration verification", async ({ page }) => {
        // Test that environment is properly configured
        await page.goto("/admin/login");

        // Check that we're in the correct environment
        const title = await page.title();
        expect(title).toContain("Assets Manager");

        // Check that debug mode is working (in development)
        const response = await page.goto("/nonexistent-route");
        expect(response.status()).toBe(404);

        // In debug mode, should show detailed error page
        const content = await page.content();
        expect(content.length).toBeGreaterThan(100); // Should have error content
    });

    test("Database configuration test", async ({ page }) => {
        // Test database connectivity through admin login
        await page.goto("/admin/login");

        // Login requires database access
        await page.fill('input[type="email"]', "<EMAIL>");
        await page.fill('input[type="password"]', "password");
        await page.click('button[type="submit"]');

        // Successful login means database is working
        await expect(page).toHaveURL(/.*\/admin$/);

        // Check that seeded data is accessible
        await expect(
            page.locator("text=Admin User, text=Administrator")
        ).toBeVisible();
    });

    test("File system configuration", async ({ page }) => {
        await page.goto("/admin/login");

        // Check that static assets are served correctly
        const response = await page.goto("/build/assets/app-CmHeOvdh.css");
        if (response) {
            expect(response.status()).toBe(200);
        }

        // Check that Filament assets are accessible
        await page.goto("/admin/login");

        // Wait for all network requests to complete
        await page.waitForLoadState("networkidle");

        // Check that CSS is loaded (no styling issues)
        const bodyStyles = await page.locator("body").evaluate((el) => {
            const styles = window.getComputedStyle(el);
            return {
                margin: styles.margin,
                padding: styles.padding,
                fontFamily: styles.fontFamily,
            };
        });

        expect(bodyStyles.fontFamily).toBeTruthy();
    });

    test("Session configuration", async ({ page }) => {
        await page.goto("/admin/login");

        // Test session persistence
        await page.fill('input[type="email"]', "<EMAIL>");
        await page.fill('input[type="password"]', "password");
        await page.click('button[type="submit"]');

        await expect(page).toHaveURL(/.*\/admin$/);

        // Refresh page - session should persist
        await page.reload();
        await expect(page).toHaveURL(/.*\/admin$/);

        // Should still be logged in
        await expect(page.locator("text=Dashboard")).toBeVisible();
    });

    test("Cache configuration", async ({ page }) => {
        // Test that caching is working properly
        const startTime = Date.now();

        await page.goto("/admin/login");
        const firstLoadTime = Date.now() - startTime;

        // Second load should be faster (cached)
        const startTime2 = Date.now();
        await page.reload();
        const secondLoadTime = Date.now() - startTime2;

        // Basic performance check - second load should generally be faster
        // This is a loose check since many factors affect load time
        expect(firstLoadTime).toBeGreaterThan(0);
        expect(secondLoadTime).toBeGreaterThan(0);
    });

    test("Security configuration", async ({ page }) => {
        await page.goto("/admin/login");

        // Check CSRF protection
        const csrfToken = await page
            .locator('input[name="_token"]')
            .getAttribute("value");
        expect(csrfToken).toBeTruthy();
        expect(csrfToken.length).toBeGreaterThan(20);

        // Check that forms have CSRF protection
        const form = page.locator("form");
        await expect(form.locator('input[name="_token"]')).toBeVisible();

        // Check security headers (basic check)
        const response = await page.goto("/admin/login");
        const headers = response.headers();

        // Should have some security-related headers
        expect(headers["content-type"]).toContain("text/html");
    });

    test("Routing configuration", async ({ page }) => {
        // Test that routes are properly configured

        // Root should work
        const rootResponse = await page.goto("/");
        expect(rootResponse.status()).toBeLessThan(400);

        // Admin routes should work
        const adminResponse = await page.goto("/admin");
        expect(adminResponse.status()).toBeLessThan(400);

        // Should redirect to login
        await expect(page).toHaveURL(/.*\/admin\/login/);

        // 404 for non-existent routes
        const notFoundResponse = await page.goto("/nonexistent-route-12345");
        expect(notFoundResponse.status()).toBe(404);
    });

    test("Middleware configuration", async ({ page }) => {
        // Test that middleware is working

        // Unauthenticated access to admin should redirect
        await page.goto("/admin");
        await expect(page).toHaveURL(/.*\/admin\/login/);

        // Login and test authenticated access
        await page.fill('input[type="email"]', "<EMAIL>");
        await page.fill('input[type="password"]', "password");
        await page.click('button[type="submit"]');

        // Should now have access to admin
        await expect(page).toHaveURL(/.*\/admin$/);

        // Direct access to admin should work when authenticated
        await page.goto("/admin");
        await expect(page).toHaveURL(/.*\/admin$/);
    });
});
